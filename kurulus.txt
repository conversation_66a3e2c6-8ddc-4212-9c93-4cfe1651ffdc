
npx create-next-app@latest

npm create sanity@latest -- --project mz841rpk --dataset production --template clean --typescript --output-path studio-ecommerce-build

npm i next-sanity@latest --legacy--peer-deps


.env.local
NEXT_PUBLIC_SANITY_PROJECT_ID="mz841rpk"
NEXT_PUBLIC_SANITY_DATASET="production"
NEXT_PUBLIC_PUBLIC_BASE_URL="http://localhost:3000"

SANITY_STUDIO_PROJECT_ID="mz841rpk"
SANITY_STUDIO_DATASET="production"


npm install @clerk/nextjs --legacy--peer-deps


npx shadcn@latest init

npx shadcn@latest add button

npm install --global sanity@latest


sanity schema extract
npx sanity@latest schema extract  

sanity typegen generate
npx sanity@latest typegen generate

npm install framer-motion

npm install -D @tailwindcss/typography --legacy-peer-deps


npx --legacy-peer-deps shadcn@latest add popover command

npm i zustand ----legacy-peer-deps

npm i stripe ----legacy-peer-deps
