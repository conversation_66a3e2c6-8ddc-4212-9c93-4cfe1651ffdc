"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@radix-ui/react-popover";
import { Button } from "./button";
import { Check, ChevronDownIcon } from "lucide-react";
import { Category } from "@/sanity.types";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandEmpty,
} from "cmdk";
import { cn } from "@/lib/utils";

interface CategorySelectorProps {
  categories: Category[];
}

export default function CategorySelectorComponent({
  categories,
}: CategorySelectorProps) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<string | null>(null);
  const router = useRouter();
  

  return (

  <Popover open={open} onOpenChange={setOpen}>
    <PopoverTrigger asChild>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className="w-full  max-w-xs relative flex justify-center sm:justify-start sm:flex-none items-center
            space-x-2 bg-blue-500 hover:bg-blue-700 hover:text-white text-white font-bold py-2 px-4 rounded"
      >
        {value
          ? categories.find((category) => category._id === value)?.title
          : "Filter by category"}
        <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0" />
      </Button>
    </PopoverTrigger>
    <PopoverContent className="w-full max-w-xs p-0 z-50 rounded-md border bg-popover text-popover-foreground shadow-md">
      <Command>
        <CommandInput
          placeholder="Search category..."
          className="h-9"
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              const selectedCategory = categories.find((c) =>
                c.title
                  ?.toLowerCase()
                  .includes(e.currentTarget.value.toLowerCase())
              );
              if (selectedCategory?.slug?.current) {
                setValue(selectedCategory._id);
                router.push(`/categories/${selectedCategory.slug.current}`);
                setOpen(false);
              }
            }
          }}
        />
        <CommandList>
          <CommandEmpty>No category found.</CommandEmpty>
          <CommandGroup>
            {categories.map((category) => (
              <CommandItem 
              key={category._id} 
              value={category.title}
              onSelect={() => {
                setValue(value === category._id ? null : category._id);
                router.push(`/categories/${category.slug?.current}`);
                setOpen(false);
              }}
              >
                {category.title}
                <Check
                    className={cn(
                        "ml-auto h-4 w-4",
                        value === category._id ? "opacity-100" : "opacity-0"
                    )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </Command>
    </PopoverContent>
  </Popover>
  );
}
