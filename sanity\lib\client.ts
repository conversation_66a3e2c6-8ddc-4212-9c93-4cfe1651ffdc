import { createClient } from 'next-sanity'

import { apiVersion, dataset, projectId } from '../env'

export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: '2023-05-03',
  useCdn: false, // CDN'i devre dışı bırakarak en güncel veriyi alıyoruz
  token: process.env.SANITY_API_TOKEN,
  stega: {
    studioUrl:
      process.env.VERCEL_URL && process.env.VERCEL_URL !== ''
        ? `https://${process.env.VERCEL_URL}/studio`
        : `${process.env.NEXT_PUBLIC_BASE_URL}/studio`,
  },
})
