import Image from "next/image";
import { Button } from "@/components/ui/button";
import { getAllProducts } from "@/sanity/lib/products/getAllProducts";
import { getAllCategories } from "@/sanity/lib/products/getAllCategories";
import ProductsView from "@/components/productsView";
import { Category, Product } from "@/sanity.types";
import BlackFridayBanner from "@/components/BlackFridayBanner";




interface ProductsViewProps {
    products: Product[];
    categories: Category[];
}



export default async function Home() {
  const productsData = await getAllProducts();
  const categories = await getAllCategories();

  // Tip güvenliği için dönüştürme yapıyoruz
  const products = Array.isArray(productsData) ? productsData : [];

  return (
    <div>
      <BlackFridayBanner/>

      <div className="flex flex-col w-full items-center justify-top min-h-screen bg-gray-100 p-4">
        <ProductsView products={products} categories={categories} />
      </div>
    </div>
  );
}
