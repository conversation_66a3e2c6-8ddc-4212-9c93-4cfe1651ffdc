import { client } from "@/sanity/lib/client";
import { Product } from "@/sanity.types";

export const getAllProducts = async (): Promise<Product[]> => {
 
    const ALL_PRODUCTS_QUERY = `*[_type == "product"] {
        _id,
        _type,
        name,
        "slug": slug.current,
        image,
        price,
        description,
        category[]->{
            _id,
            title,
            "slug": slug.current
        },
        isFeatured,
        stock
    }`; 

    try {
        // Veriyi 'sanityFetch' yerine doğrudan 'client.fetch' ile çekiyoruz
        const products = await client.fetch(ALL_PRODUCTS_QUERY);

        console.log("Doğrudan client.fetch ile çekilen ürünler:", products);
        
        if (Array.isArray(products)) {
            return products;
        }

        return [];

    } catch (error) {
        console.error("Doğrudan client.fetch ile ürünler çekilirken hata oluştu:", error);
        return [];
    }
};