import { BasketIcon } from "@sanity/icons";
import { defineArrayMember, defineField, defineType } from "sanity";

export const orderType = defineType({
    name: 'order',
    title: 'Order',
    type: 'document',
    icon: BasketIcon,
    fields: [
        defineField({
            name: 'orderNumber',
            title: 'Order Number',
            type: 'string',
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'customerName',
            title: 'Customer Name',
            type: 'string',
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'customerEmail',
            title: 'Customer Email',
            type: 'string',
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'clerkUserId',
            title: 'Clerk User ID',
            type: 'string',
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'orderItems',
            title: 'Order Items',
            type: 'array',
            of: [
                defineArrayMember({
                    type: 'object',
                    fields: [
                        defineField({
                            name: 'product',
                            title: 'Product',
                            type: 'reference',
                            to: [{ type: 'product' }],
                            validation: (Rule) => Rule.required(),
                        }),
                        defineField({
                            name: 'quantity',
                            title: 'Quantity',
                            type: 'number',
                            validation: (Rule) => Rule.required().min(1),
                        }),
                        defineField({
                            name: 'price',
                            title: 'Price',
                            type: 'number',
                            validation: (Rule) => Rule.required().min(0),
                        }),
                    ],
                }),
            ],
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'totalAmount',
            title: 'Total Amount',
            type: 'number',
            validation: (Rule) => Rule.required().min(0),
        }),
        defineField({
            name: 'paymentIntentId',
            title: 'Payment Intent ID',
            type: 'string',
            validation: (Rule) => Rule.required(),
        }),
        defineField({
            name: 'orderStatus',
            title: 'Order Status',
            type: 'string',
            options: {
                list: [
                    { title: 'Pending', value: 'pending' },
                    { title: 'Processing', value: 'processing' },
                    { title: 'Shipped', value: 'shipped' },
                    { title: 'Delivered', value: 'delivered' },
                    { title: 'Cancelled', value: 'cancelled' },
                ],
            },
            validation: (Rule) => Rule.required(),
            initialValue: 'pending',
        }),
        defineField({
            name: 'paymentStatus',
            title: 'Payment Status',
            type: 'string',
            options: {
                list: [
                    { title: 'Pending', value: 'pending' },
                    { title: 'Paid', value: 'paid' },
                    { title: 'Failed', value: 'failed' },
                    { title: 'Refunded', value: 'refunded' },
                ],
            },
            validation: (Rule) => Rule.required(),
            initialValue: 'pending',
        }),
        defineField({
            name: 'shippingAddress',
            title: 'Shipping Address',
            type: 'object',
            fields: [
                defineField({
                    name: 'street',
                    title: 'Street',
                    type: 'string',
                }),
                defineField({
                    name: 'city',
                    title: 'City',
                    type: 'string',
                }),
                defineField({
                    name: 'state',
                    title: 'State',
                    type: 'string',
                }),
                defineField({
                    name: 'postalCode',
                    title: 'Postal Code',
                    type: 'string',
                }),
                defineField({
                    name: 'country',
                    title: 'Country',
                    type: 'string',
                }),
            ],
        }),
        defineField({
            name: 'createdAt',
            title: 'Created At',
            type: 'datetime',
            validation: (Rule) => Rule.required(),
            initialValue: () => new Date().toISOString(),
        }),
    ],
    preview: {
        select: {
            orderNumber: 'orderNumber',
            customerName: 'customerName',
            totalAmount: 'totalAmount',
            status: 'orderStatus',
        },
        prepare(selection) {
            const { orderNumber, customerName, totalAmount, status } = selection;
            return {
                title: `Order ${orderNumber}`,
                subtitle: `${customerName} - $${totalAmount} - ${status}`,
                media: BasketIcon,
            };
        },
    },
});
        