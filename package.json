{"name": "15.0.2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typegen:extract": "npx sanity@latest schema extract", "typegen:generate": "npx sanity@latest typegen generate", "typegen": "npm run typegen:extract && npm run typegen:generate"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@sanity/icons": "^3.7.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.91.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-sanity": "^9.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-is": "^19.1.0", "sanity": "^3.91.0", "stripe": "^18.2.1", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}