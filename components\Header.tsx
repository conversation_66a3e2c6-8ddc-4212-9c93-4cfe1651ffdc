"use client";

import {
  SignIn<PERSON><PERSON><PERSON>,
  SignedIn,
  SignedO<PERSON>,
  User<PERSON><PERSON>on,
  useUser,
} from "@clerk/nextjs";
import React from "react";
import Link from "next/link";
import Form from "next/form";
import { PackageIcon } from "lucide-react";
import { ClerkLoaded } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { useBasketStore } from "@/app/(store)/store";


function Header() {
  const { user, isLoaded } = useUser();
  const itemCount = useBasketStore((state) =>
     state.items.reduce((acc, item) => acc + item.quantity, 0));

  const createClerkPasskey = async () => {
    try {
      const response = await user?.createPasskey();
      console.log(response);
    } catch (err) {
      console.error("Error creating passkey:", JSON.stringify(err, null, 2));
    }
  };

  return (
    <header className="flex flew-wrap items-center justify-between p-4 py-8">
      {/* {Top row of header} */}
      <div className="flex flex-wrap w-full items-center justify-between px-4">
        <Link
          href="/"
          className="text-2xl
                     font-bold
                      text-color-full-500
                       hover:text-color-full-500
                        hover:opacity-50 
                        cursor-pointer
                         mx-auto 
                         sm:mx-0"
        >
          Trendfy_X
        </Link>

        <Form
          action="/search"
          className="sm:w-auto sm:flex-1 sm:mx-4 mt-4 sm:mt-0"
        >
          <input
            type="text"
            name="query"
            placeholder="Search for products"
            className="bg-gray-100
                        text-gray-800
                         rounded
                         focus:outline-none
                         focus:ring-2
                         focus:ring-blue-500
                         focus:ring-offset-2
                         border
                         w-full
                         max-w-4xl"
          />
        </Form>

        <div className="flex items-center space-x-4 mt-4 sm:mt-0 flex-1 sm:flex-none">
          <Link
            href="/basket"
            className="flex-1 relative flex justify-center sm:justify-start 
                        sm:flex-none items-center space-x-2 bg-blue-500 hover:bg-blue-700 
                        text-white font-bold py-2 px-4 rounded"
          >
            <PackageIcon className="w-6 h-6" />
            {/* {span item count once global state is implemented} */}

              <span className="absolute top-2 -right-2 bg-red-500 text-white 
            rounded-full w-5 h-5 flex items-center justify-center text-xs font-semibold">
              {itemCount}
            </span>

            <span>My Basket</span>
          </Link>

          <ClerkLoaded>
            {/* {Sign in button} */}
            <SignedIn>
              <Link
                href="/orders"
                className="flex-1 relative flex justify-center sm:justify-start 
                        sm:flex-none items-center space-x-2 bg-blue-500 hover:bg-blue-700
                         text-white font-bold py-2 px-4 rounded"
              >
                <PackageIcon className="w-6 h-6" />
                <span>My Orders</span>
              </Link>
            </SignedIn>

            {user ? (
              <div className="flex items-center space-x-2">
                <UserButton />
                <div className="hidden sm:block text-sm text-gray-500">
                  <p className="text-gray-400">Welcome Back</p>
                  <p className="font-bold">{user.fullName}</p>
                </div>
              </div>
            ) : (
              <SignInButton mode="modal" />
            )}

            {user?.passkeys.length === 0 && (
              <Button
                onClick={createClerkPasskey}
                className="bg-white
                                     hover:bg-blue-700 hover:text-white animate-pulse
                                     text-blue-500 font-bold py-2 px-4 rounded
                                     border border-blue-300"
              >
                Create Passkey
              </Button>
            )}
          </ClerkLoaded>
        </div>
      </div>
    </header>
  );
}

export default Header;
