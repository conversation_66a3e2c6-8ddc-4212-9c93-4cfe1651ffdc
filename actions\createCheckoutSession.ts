"use server";

import { BasketItem } from "@/app/(store)/store";
import { imageUrl } from "@/lib/imageUrl";
import stripe from "@/lib/stripe";


export type Metadata = {
    oderNumber: string;
    customer: string;
    customerName: string;
    clerkUserId: string;
    customerEmail: string;
    customerPhone: string;
};

export type GroupedBasketItems = {
    product: BasketItem["product"];
    category: string;
    quantity: number;
    price: number;
    deliveryFee: number;
    items: BasketItem[];
    totalPrice: number;
    totalQuantity: number;
    totalDeliveryFee: number;
};

export async function createCheckoutSession(
    Items: GroupedBasketItems[], 
    metadata: Metadata
) {
    try {
        const appUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
        const itemsWithoutPrice = Items.filter((item) => !item.product.price);
        if (itemsWithoutPrice.length > 0) {
            throw new Error("Price is required for each item");
        }

        // search for existing customer by email
        const customers = await stripe.customers.list({
            email: metadata.customerEmail,
            limit: 1,
        });

        let customerId: string | undefined;

        if (customers.data.length > 0) {
            customerId= customers.data[0].id;

            }

        
        
        // const baseUrl = process.env.VERCEL_URL 
        // ? `https://${process.env.VERCEL_URL}` 
        // : process.env.NEXT_PUBLIC_BASE_URL;
            

        // const successUrl = `${baseUrl}/success?session_id={CHECKOUT_SESSION_ID}&orderNumber=${metadata.oderNumber}`;
        // const cancelUrl = `${baseUrl}/basket`;


        const session = await stripe.checkout.sessions.create(
            {
                customer: customerId,
                payment_method_types: ["card"],
                mode: "payment",
                metadata,
                allow_promotion_codes: true,
                success_url: `${appUrl}/success?session_id={CHECKOUT_SESSION_ID}&orderNumber=${metadata.orderNumber}`,
                cancel_url: `${appUrl}/basket`,
                line_items: Items.map((item) => ({
                    price_data: {
                        currency: "usd",
                        unit_amount: Math.round(item.product.price! * 100),
                        product_data: {
                            name: item.product.name || "Unnamed Product",
                            description: `Product ID: ${item.product._id}`,
                            metadata: {
                                sanity_id: item.product._id,
                            },
                            images: item.product.image
                            ? [imageUrl(item.product.image).url]
                            : undefined,
                        },
                    },
                         quantity: item.quantity,
                })),

            });

            return session.url;
    } catch (error) {
        console.error("Error creating checkout session", error);
        throw error;
    }
}




