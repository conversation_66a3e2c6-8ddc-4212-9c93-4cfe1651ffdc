"use client";

import { useBasketStore, BasketItem } from "@/app/(store)/store";
import { Button } from "@/components/ui/button";
import { SignInButton, useAuth, useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import AddToBasketButton from "@/components/AddToBasketButton";
import Image from "next/image";
import { urlForImage } from "@/sanity/lib/image";
import Loader from "@/components/Loader";
import { createCheckoutSession, GroupedBasketItems } from "@/actions/createCheckoutSession";
import { formatCurrency } from "@/lib/formatCurrency";

function BasketPage() {
    const items = useBasketStore((state) => state.items);
    const {isSignedIn} = useAuth();
    const {user} = useUser();
    const router = useRouter();
    const [isClient, setIsClient] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    if(items.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-screen">
                <h1 className="text-2xl font-bold">Your basket is empty</h1>
                <p className="text-sm text-gray-500 mb-4">
                    Add items to your basket to get started.
                </p>
                <Button onClick={() => router.push("/")} className="bg-blue-500 hover:bg-blue-600">
                    Continue Shopping
                </Button>
            </div>
        )
    }

    const getImageUrl = (image: any) => {
        if (!image || !image.asset?._ref) return null;
        return urlForImage(image).url();
    };

    const handleCheckout = async () => {
        if (!isSignedIn || !user) return;
        setIsLoading(true);

        try {
            const metadata = {
                orderNumber: crypto.randomUUID(),
                customer: user.id,
                customerEmail: user.emailAddresses[0].emailAddress,
                customerName: user.fullName ?? user.firstName ?? "Guest",
                clerkUserId: user.id,
                customerPhone: "",
            };

            const checkoutUrl = await createCheckoutSession(items, metadata);
            if (checkoutUrl) {
                window.location.href = checkoutUrl;
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    }

    if (!isClient) {
        return <Loader/>
    }

  return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-2xl font-bold mb-8">Shopping Basket</h1>
            
            <div className="flex flex-col lg:flex-row gap-8">
                <div className="flex-1">
                    <div className="bg-white rounded-lg shadow">
                        <ul className="divide-y divide-gray-200">
                            {items.map((item) => {
                                const imageUrl = getImageUrl(item.product.image);
                                return (
                                    <li key={item.product._id} className="p-4 sm:p-6">
                                        <div className="flex items-center">
                                            {imageUrl && (
                                                <div className="relative h-24 w-24 flex-shrink-0">
                                                    <Image
                                                        src={imageUrl}
                                                        alt={item.product.name || "Product image"}
                                                        fill
                                                        className="object-cover rounded-md"
                                                    />
                                                </div>
                                            )}
                                            <div className="ml-6 flex-1">
                                                <div className="flex items-center justify-between">
                                                    <h2 className="text-lg font-medium text-gray-900">
                                                        {item.product.name}
                                                    </h2>
                                                    <p className="text-lg font-medium text-gray-900">
                                                        {formatCurrency(item.product.price || 0)}
                                                    </p>
                                                </div>
                                                <div className="mt-4 flex items-center justify-between">
                                                    <div className="flex items-center">
                                                        <AddToBasketButton product={item.product} />
                                                    </div>
                                                    <p className="text-sm text-gray-500">
                                                        Quantity: {item.quantity}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                );
                            })}
                        </ul>
                    </div>
                </div>

                <div className="w-full lg:w-96">
                    <div className="bg-white p-6 rounded-lg shadow sticky top-4">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Order Summary</h2>
                        <div className="space-y-4">
                            <div className="flex justify-between">
                                <p className="text-gray-600">Subtotal</p>
                                <p className="font-medium">
                                    {formatCurrency(useBasketStore.getState().getTotalPrice())}
                                </p>
                            </div>
                            <div className="border-t pt-4">
                                <div className="flex justify-between">
                                    <p className="text-lg font-medium">Total</p>
                                    <p className="text-lg font-medium">
                                        {formatCurrency(useBasketStore.getState().getTotalPrice())}
                                    </p>
                                </div>
                            </div>
                            {isSignedIn && user ? (
                                <Button 
                                    onClick={handleCheckout}
                                    disabled={isLoading}
                                    className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:bg-gray-400"
                                >
                                    {isLoading ? "Processing..." : "Checkout"}
                                </Button>
                            ) : (
                                <SignInButton mode="modal">
                                    <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                                        Sign in to checkout
                                    </Button>
                                </SignInButton>
                            )}
                        </div>
                    </div>
                </div>
            </div>
    </div>
    );
}

export default BasketPage;