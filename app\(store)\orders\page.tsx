"use client";

import { redirect } from 'next/navigation'
import { useAuth } from '@clerk/nextjs'
import { getMyOrders } from '@/sanity/lib/orders/getMyOrders'
import { formatCurrency } from '@/lib/formatCurrency'
import { format } from 'date-fns'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import Loader from '@/components/Loader'
import { urlForImage } from '@/sanity/lib/image'

type OrderItem = {
    quantity: number;
    price: number;
    product: {
        name: string;
        image?: {
            asset?: {
                _ref: string;
            };
        };
    };
}

type Order = {
    _id: string;
    orderNumber: string;
    customerName: string;
    totalAmount: number;
    orderStatus: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
    createdAt: string;
    orderItems: OrderItem[];
    discountAmount?: number;
    discountCode?: string;
}

function Orders() {   
    const { userId, isLoaded } = useAuth()
    const [orders, setOrders] = useState<Order[]>([])
    const [loading, setLoading] = useState(true)
    const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null)

    useEffect(() => {
        async function fetchOrders() {
            if (!userId) return
            try {
                const fetchedOrders = await getMyOrders(userId) as Order[]
                setOrders(fetchedOrders)
            } catch (error) {
                console.error('Error fetching orders:', error)
            } finally {
                setLoading(false)
            }
        }

        if (isLoaded && !userId) {
        redirect('/sign-in')
    }

        if (userId) {
            fetchOrders()
        }
    }, [userId, isLoaded])

    if (!isLoaded || loading) {
        return <Loader />
    }

    const getImageUrl = (image: OrderItem['product']['image']) => {
        if (!image || !image.asset?._ref) return null;
        return urlForImage(image).url();
    };

    const toggleOrderExpansion = (orderId: string) => {
        setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
    };

  return (
    <div className='flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50'>
            <div className='bg-white p-4 sm:p-8 rounded-xl shadow-lg w-full max-w-4xl'>
                <h1 className='text-2xl font-bold text-gray-900 tracking-tight mb-8'>
                    My Orders
            </h1>

                {orders.length === 0 ? (
                <div className='text-center text-gray-500'>
                        <p>You have no orders yet</p>
                </div>
            ) : (
                    <div className='flex flex-col gap-6'>
                        {orders.map((order: Order) => (
                            <div 
                                key={order._id} 
                                className='border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer'
                                onClick={() => toggleOrderExpansion(order._id)}
                            >
                                <div className='flex justify-between items-start mb-4'>
                                    <div>
                                        <p className='text-sm text-gray-500'>Order Number</p>
                                        <p className='font-medium'>{order.orderNumber}</p>
                                        <p className='text-sm text-gray-500 mt-2'>
                                            {format(new Date(order.createdAt), 'MMM dd, yyyy HH:mm')}
                                        </p>
                                    </div>
                                    <div className='text-right'>
                                        <p className='text-sm text-gray-500'>Total Amount</p>
                                        <p className='font-medium'>{formatCurrency(order.totalAmount)}</p>
                                        <span className={`inline-block px-2 py-1 text-xs rounded-full mt-2 ${
                                            order.orderStatus === 'delivered' ? 'bg-green-100 text-green-800' :
                                            order.orderStatus === 'shipped' ? 'bg-blue-100 text-blue-800' :
                                            order.orderStatus === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                                            order.orderStatus === 'cancelled' ? 'bg-red-100 text-red-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {order.orderStatus.charAt(0).toUpperCase() + order.orderStatus.slice(1)}
                                        </span>
                                    </div>
                                </div>
                                
                                {expandedOrderId === order._id && (
                                    <>
                                        <div className='mt-4 pt-4 border-t'>
                                            <div className='space-y-2 mb-4'>
                                                <div className='flex justify-between text-sm'>
                                                    <span className='text-gray-500'>Subtotal:</span>
                                                    <span>{formatCurrency(order.totalAmount + (order.discountAmount || 0))}</span>
                                                </div>
                                                {order.discountAmount && order.discountCode && (
                                                    <div className='flex justify-between text-sm'>
                                                        <span className='text-gray-500'>Discount ({order.discountCode}):</span>
                                                        <span className='text-green-600'>-{formatCurrency(order.discountAmount)}</span>
                                                    </div>
                                                )}
                                                <div className='flex justify-between text-base font-medium pt-2 border-t'>
                                                    <span>Total:</span>
                                                    <span>{formatCurrency(order.totalAmount)}</span>
                                                </div>
                                            </div>

                                            <p className='text-sm text-gray-500 mb-2'>Items</p>
                                            <div className='space-y-4'>
                                                {order.orderItems.map((item: OrderItem, index: number) => {
                                                    const imageUrl = getImageUrl(item.product.image);
                                                    return (
                                                        <div key={index} className='flex items-center gap-4 bg-gray-50 p-3 rounded-lg'>
                                                            {imageUrl && (
                                                                <div className='relative w-16 h-16 flex-shrink-0'>
                                                                    <Image
                                                                        src={imageUrl}
                                                                        alt={item.product.name}
                                                                        fill
                                                                        className='object-cover rounded'
                                                                    />
                                                                </div>
                                                            )}
                                                            <div className='flex-1'>
                                                                <p className='font-medium'>{item.product.name}</p>
                                                                <div className='flex justify-between items-center mt-1'>
                                                                    <p className='text-sm text-gray-500'>
                                                                        Quantity: {item.quantity}
                                                                    </p>
                                                                    <p className='text-sm font-medium'>
                                                                        {formatCurrency(item.price)}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </>
                                )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    </div>
  )
}

export default Orders;