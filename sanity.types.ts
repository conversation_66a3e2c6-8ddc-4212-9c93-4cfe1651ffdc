/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type Order = {
  _id: string;
  _type: "order";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderNumber?: string;
  customerName?: string;
  customerEmail?: string;
  clerkUserId?: string;
  orderItems?: Array<{
    product?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "product";
    };
    quantity?: number;
    price?: number;
    _key: string;
  }>;
  totalAmount?: number;
  paymentIntentId?: string;
  orderStatus?: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  paymentStatus?: "pending" | "paid" | "failed" | "refunded";
  shippingAddress?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  createdAt?: string;
};

export type Sales = {
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
};

export type Product = {
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  stock?: number;
};

export type Category = {
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
};

export type BlockContent = Array<{
  children?: Array<{
    marks?: Array<string>;
    text?: string;
    _type: "span";
    _key: string;
  }>;
  style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote";
  listItem?: "bullet";
  markDefs?: Array<{
    href?: string;
    _type: "link";
    _key: string;
  }>;
  level?: number;
  _type: "block";
  _key: string;
} | {
  asset?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
  };
  media?: unknown;
  hotspot?: SanityImageHotspot;
  crop?: SanityImageCrop;
  alt?: string;
  _type: "image";
  _key: string;
}>;

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes = Order | Sales | Product | Category | BlockContent | SanityImagePaletteSwatch | SanityImagePalette | SanityImageDimensions | SanityImageHotspot | SanityImageCrop | SanityFileAsset | SanityImageAsset | SanityImageMetadata | Geopoint | Slug | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: ./sanity/lib/orders/getMyOrders.tsx
// Variable: MY_ORDERS_QUERY
// Query: *[_type == "order" && user._ref == $userId] | order(orderDate desc) {        ...,        products[]->{            ...,            product->{            }        }    }
export type MY_ORDERS_QUERYResult = Array<{
  _id: string;
  _type: "order";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderNumber?: string;
  customerName?: string;
  customerEmail?: string;
  clerkUserId?: string;
  orderItems?: Array<{
    product?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "product";
    };
    quantity?: number;
    price?: number;
    _key: string;
  }>;
  totalAmount?: number;
  paymentIntentId?: string;
  orderStatus?: "cancelled" | "delivered" | "pending" | "processing" | "shipped";
  paymentStatus?: "failed" | "paid" | "pending" | "refunded";
  shippingAddress?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  createdAt?: string;
  products: null;
}>;

// Source: ./sanity/lib/products/getAllCategories.ts
// Variable: ALL_CATEGORIES_QUERY
// Query: *[        _type == "category"        ] |  order(name asc)
export type ALL_CATEGORIES_QUERYResult = Array<{
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
}>;

// Source: ./sanity/lib/products/getProductBySlug.ts
// Variable: PRODUCT_BY_SLUG_QUERY
// Query: *[            _type == "product" && slug.current == $slug        ] | order(name asc) [0]
export type PRODUCT_BY_SLUG_QUERYResult = {
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  stock?: number;
} | null;

// Source: ./sanity/lib/products/getProductsByCategory.ts
// Variable: PRODUCTS_BY_CATEGORY_QUERY
// Query: *[            _type == "product"             && $categorySlug in category[]->slug.current        ]
export type PRODUCTS_BY_CATEGORY_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  stock?: number;
}>;

// Source: ./sanity/lib/products/searchProductsByName.ts
// Variable: SEARCH_PRODUCTS_BY_NAME_QUERY
// Query: *[            _type == "product"             && name match $searchParam        ]
export type SEARCH_PRODUCTS_BY_NAME_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  stock?: number;
}>;

// Source: ./sanity/lib/sales/getAciveSaleByCouponCode.ts
// Variable: ACTIVE_SALES_BY_COUPON_QUERY
// Query: *[            _type == "sales" &&            isActive == true &&            couponCode == $couponCode        ] | order(validityFrom desc) [0]
export type ACTIVE_SALES_BY_COUPON_QUERYResult = {
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
} | null;
// Variable: ACTIVE_SALES_BY_COUPON_CODES_QUERY
// Query: *[            _type == "sales" &&            isActive == true &&            couponCode in $couponCodes        ] | order(validityFrom desc)
export type ACTIVE_SALES_BY_COUPON_CODES_QUERYResult = Array<{
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
}>;

// Query TypeMap
import "@sanity/client";
declare module "@sanity/client" {
  interface SanityQueries {
    "\n    *[_type == \"order\" && user._ref == $userId] | order(orderDate desc) {\n        ...,\n        products[]->{\n            ...,\n            product->{\n            }\n        }\n    }\n    ": MY_ORDERS_QUERYResult;
    "*[\n        _type == \"category\"\n        ] |  order(name asc)\n        ": ALL_CATEGORIES_QUERYResult;
    "\n        *[\n            _type == \"product\" && slug.current == $slug\n        ] | order(name asc) [0]\n    ": PRODUCT_BY_SLUG_QUERYResult;
    "\n        *[\n            _type == \"product\" \n            && $categorySlug in category[]->slug.current\n        ]\n    ": PRODUCTS_BY_CATEGORY_QUERYResult;
    "\n        *[\n            _type == \"product\" \n            && name match $searchParam\n        ]\n    ": SEARCH_PRODUCTS_BY_NAME_QUERYResult;
    "\n        *[\n            _type == \"sales\" &&\n            isActive == true &&\n            couponCode == $couponCode\n        ] | order(validityFrom desc) [0]\n    ": ACTIVE_SALES_BY_COUPON_QUERYResult;
    "\n        *[\n            _type == \"sales\" &&\n            isActive == true &&\n            couponCode in $couponCodes\n        ] | order(validityFrom desc)\n    ": ACTIVE_SALES_BY_COUPON_CODES_QUERYResult;
  }
}
